# 李宁劳动力预测Mock数据集

## 数据概述

本数据集基于李宁2022-2024年真实业务数据特征生成，包含5个门店3年的完整业务数据，用于劳动力预测Agent的训练和测试。

## 数据规模

- **时间范围**: 2022年1月1日 - 2024年12月31日 (3年)
- **门店数量**: 5个 (覆盖上海、北京、广州、深圳、成都)
- **总销售额**: 13.29亿元 (符合李宁实际规模)
- **交易记录**: 1,073,054笔
- **客流记录**: 65,760条 (按小时统计)

## 文件说明

### CSV数据文件

1. **store.csv** - 门店信息表 (5条记录)
   - 包含门店基本信息、地址、面积、租金等

2. **product.csv** - 商品信息表 (105条记录)
   - 鞋类54个 (51.4%) - 符合李宁鞋类占比54.7%
   - 服装43个 (41.0%) - 符合李宁服装占比43%
   - 器材配件8个 (7.6%) - 符合李宁配件占比8%

3. **employee.csv** - 员工信息表 (104条记录)
   - 按门店规模配置员工数量
   - 包含店长、副店长、导购、收银员等岗位

4. **member.csv** - 会员信息表 (1,979条记录)
   - 会员等级分布：PLATINUM(60%), GOLD(19.8%), SILVER(10.2%), BRONZE(10%)
   - 会员交易占比40%

5. **promotion.csv** - 促销活动表 (30条记录)
   - 涵盖春节、618、双11等重要促销节点
   - 包含折扣率、预期提升效果等

6. **schedule.csv** - 排班数据表 (81,495条记录)
   - 3年每日排班数据
   - 包含早班、中班、晚班、全天班等班次

7. **attendance.csv** - 考勤记录表 (77,483条记录)
   - 基于排班生成的实际考勤数据
   - 包含迟到、早退、加班等状态

8. **traffic.csv** - 客流数据表 (65,760条记录)
   - 按小时统计的进店客流
   - 包含天气、温度、节假日等影响因素
   - 平均转化率12.99%

9. **transaction.csv** - 交易数据表 (1,073,054条记录)
   - 每笔交易的详细信息
   - 平均客单价¥1,238.41
   - 支持微信、支付宝、现金、银行卡等支付方式

10. **forecast_history.csv** - 预测历史表 (720条记录)
    - 模拟AI预测系统的历史预测记录
    - 包含预测准确度评估

### SQL建表文件

- **create_tables.sql** - 完整的数据库建表语句
  - 包含所有表结构定义
  - 设置了合适的主键、外键关系
  - 符合MySQL语法规范

## 数据特征

### 业务指标符合李宁实际情况

1. **年度销售额增长率**:
   - 2022年: 基准年
   - 2023年: +1.1% (符合李宁2023年+7.0%的趋势)
   - 2024年: -8.0% (反映市场调整)

2. **商品结构**:
   - 鞋类平均价格¥819 (符合李宁中高端定位)
   - 服装平均价格¥375 (合理的服装价格区间)
   - 器材配件平均价格¥493

3. **季节性特征**:
   - 11-12月销售高峰 (双11、年终促销)
   - 6-8月夏季服装销售旺季
   - 符合运动品牌季节性规律

### 门店差异化

- **大型店铺** (上海、广州): 年销售额3.1亿+
- **中型店铺** (北京、深圳): 年销售额2.0-2.5亿
- **小型店铺** (成都): 年销售额1.5亿

### 客流与转化

- **平均转化率**: 12.99% (行业合理水平)
- **客流高峰**: 11-13点、18-20点
- **周末效应**: 客流提升40%
- **节假日效应**: 客流提升60%

## 使用说明

### 数据导入

1. 使用提供的SQL文件创建数据库表结构
2. 将CSV文件导入对应的数据表
3. 验证外键关系完整性

### 适用场景

- 劳动力需求预测模型训练
- 客流预测算法开发
- 销售预测模型验证
- 排班优化算法测试
- 门店运营分析

### 数据质量保证

- 所有外键关系完整
- 时间序列连续无断点
- 业务逻辑合理 (如客流与销售的相关性)
- 数据分布符合真实业务场景

## 技术实现

- **生成工具**: JavaScript (Node.js)
- **数据格式**: CSV (UTF-8编码)
- **SQL方言**: MySQL
- **生成时间**: 约2分钟 (取决于硬件性能)

## 注意事项

1. 本数据集为模拟数据，仅用于算法开发和测试
2. 数据特征基于李宁公开财报信息生成
3. 个人信息均为虚构，不涉及真实个人隐私
4. 建议在使用前进行数据质量验证

## 联系信息

如有数据质量问题或需要调整数据特征，请联系开发团队。
