
CREATE TABLE store (
    store_id INT PRIMARY KEY,
    store_code VARCHAR(20) NOT NULL,
    store_name VARCHAR(255) NOT NULL,
    store_type VARCHAR(100) NOT NULL,
    city VARCHAR(50) NOT NULL,
    district VARCHAR(50),
    address VARCHAR(500) NOT NULL,
    store_area DECIMAL(10,2) NOT NULL,
    rent_cost DECIMAL(10,2),
    open_date DATE NOT NULL,
    in_store_zoning VARCHAR(500),
    operating_hours_start TIME,
    operating_hours_end TIME,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);


CREATE TABLE product (
    product_id VARCHAR(50) PRIMARY KEY,
    product_name VARCHAR(255) NOT NULL,
    brand VARCHAR(100),
    type VARCHAR(100) NOT NULL,
    category VARCHAR(100) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    cost DECIMAL(10,2),
    launch_date DATE NOT NULL,
    season VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);


CREATE TABLE employee (
    employee_id INT PRIMARY KEY,
    employee_code VARCHAR(20),
    name VARCHAR(100) NOT NULL,
    gender ENUM('M','F'),
    age INT,
    phone VARCHAR(20),
    email VARCHAR(100),
    hire_date DATE,
    position VARCHAR(30),
    level VARCHAR(20),
    base_salary DECIMAL(8,2),
    commission_rate DECIMAL(5,4),
    store_id INT,
    status ENUM('ACTIVE','INACTIVE','RESIGNED') DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (store_id) REFERENCES store(store_id)
);


CREATE TABLE member (
    member_id VARCHAR(50) PRIMARY KEY,
    member_code VARCHAR(20),
    name VARCHAR(100),
    phone VARCHAR(20),
    gender ENUM('M','F'),
    age INT,
    register_date DATE,
    level VARCHAR(20),
    total_consumption DECIMAL(10,2),
    points INT,
    preferred_store_id INT,
    status ENUM('ACTIVE','INACTIVE') DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (preferred_store_id) REFERENCES store(store_id)
);


CREATE TABLE promotion (
    promotion_id BIGINT PRIMARY KEY,
    promotion_code VARCHAR(30) UNIQUE,
    promotion_name VARCHAR(100) NOT NULL,
    store_id INT,
    promotion_type VARCHAR(20) NOT NULL,
    discount_rate DECIMAL(5,2),
    min_amount DECIMAL(10,2),
    max_discount DECIMAL(10,2),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    expected_traffic_boost DECIMAL(5,2),
    expected_sales_boost DECIMAL(5,2),
    status ENUM('PENDING','ACTIVE','EXPIRED','CANCELLED') DEFAULT 'PENDING',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (store_id) REFERENCES store(store_id)
);


CREATE TABLE schedule (
    schedule_id BIGINT PRIMARY KEY,
    store_id INT NOT NULL,
    employee_id INT NOT NULL,
    schedule_date DATE NOT NULL,
    shift_type VARCHAR(20),
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    position_code VARCHAR(30) NOT NULL,
    hourly_rate DECIMAL(8,2),
    break_duration INT,
    is_overtime BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (store_id) REFERENCES store(store_id),
    FOREIGN KEY (employee_id) REFERENCES employee(employee_id)
);


CREATE TABLE attendance (
    attendance_id BIGINT PRIMARY KEY,
    employee_id INT NOT NULL,
    store_id INT NOT NULL,
    schedule_id BIGINT,
    attendance_date DATE NOT NULL,
    scheduled_start_time TIME,
    scheduled_end_time TIME,
    actual_start_time TIME,
    actual_end_time TIME,
    status VARCHAR(20),
    work_hours DECIMAL(4,1),
    overtime_hours DECIMAL(4,1),
    late_minutes INT,
    early_leave_minutes INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (employee_id) REFERENCES employee(employee_id),
    FOREIGN KEY (store_id) REFERENCES store(store_id),
    FOREIGN KEY (schedule_id) REFERENCES schedule(schedule_id)
);


CREATE TABLE traffic (
    traffic_id BIGINT PRIMARY KEY,
    store_id INT NOT NULL,
    traffic_date DATE NOT NULL,
    traffic_hour TINYINT NOT NULL,
    traffic_count_in INT,
    traffic_count_out INT,
    stay_duration_avg DECIMAL(5,2),
    conversion_rate DECIMAL(5,4),
    weather_condition VARCHAR(50),
    temperature DECIMAL(4,1),
    is_holiday BOOLEAN,
    is_weekend BOOLEAN,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (store_id) REFERENCES store(store_id)
);


CREATE TABLE transaction (
    transaction_id BIGINT PRIMARY KEY,
    product_id VARCHAR(50),
    store_id INT,
    salesperson_id INT,
    member_id VARCHAR(50),
    transaction_datetime DATETIME,
    quantity INT,
    unit_price DECIMAL(10,2),
    total_amount DECIMAL(10,2),
    discount_amount DECIMAL(10,2),
    payment_method VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES product(product_id),
    FOREIGN KEY (store_id) REFERENCES store(store_id),
    FOREIGN KEY (salesperson_id) REFERENCES employee(employee_id),
    FOREIGN KEY (member_id) REFERENCES member(member_id)
);


CREATE TABLE forecast_history (
    forecast_id BIGINT PRIMARY KEY,
    store_id INT,
    forecast_date DATE,
    forecast_hour TINYINT,
    model_version VARCHAR(20),
    predicted_traffic INT,
    predicted_sales DECIMAL(10,2),
    recommended_staff_count INT,
    recommended_positions JSON,
    forecast_confidence DECIMAL(5,2),
    weather_forecast VARCHAR(50),
    temperature_forecast DECIMAL(4,1),
    is_holiday BOOLEAN,
    is_weekend BOOLEAN,
    has_promotion BOOLEAN,
    promotion_impact_factor DECIMAL(5,2),
    actual_traffic INT,
    actual_sales DECIMAL(10,2),
    actual_staff_count INT,
    accuracy_score DECIMAL(5,2),
    forecast_created_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (store_id) REFERENCES store(store_id)
);