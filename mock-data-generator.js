const fs = require('fs');
const path = require('path');

// 工具函数
class MockDataGenerator {
    constructor() {
        this.stores = [];
        this.products = [];
        this.employees = [];
        this.members = [];
        this.promotions = [];
        this.schedules = [];
        this.attendances = [];
        this.traffic = [];
        this.transactions = [];
        this.forecastHistory = [];

        // 计数器
        this.counters = {
            transactionId: 10000,
            trafficId: 20000,
            scheduleId: 30000,
            employeeId: 2000,
            promotionId: 40000,
            attendanceId: 50000,
            forecastId: 60000
        };
    }

    // 随机数生成工具
    randomInt(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    randomFloat(min, max, decimals = 2) {
        return parseFloat((Math.random() * (max - min) + min).toFixed(decimals));
    }

    randomChoice(array) {
        return array[Math.floor(Math.random() * array.length)];
    }

    randomDate(start, end) {
        return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
    }

    formatDate(date) {
        return date.toISOString().split('T')[0];
    }

    formatDateTime(date) {
        return date.toISOString().replace('T', ' ').split('.')[0];
    }

    formatTime(date) {
        return date.toTimeString().split(' ')[0];
    }

    // 生成门店数据
    generateStores() {
        const storeData = [
            {
                store_id: 101,
                store_code: 'SH001',
                store_name: '上海市青浦区百联奥莱广场工厂店',
                store_type: '大型单层店铺(30人以上)',
                city: '上海',
                district: '青浦区',
                address: '上海市青浦区沪青平公路百联奥莱广场3层C329',
                store_area: 120.00,
                rent_cost: 25000.00,
                open_date: '2019-03-01',
                in_store_zoning: 'A,B,C,D',
                operating_hours_start: '10:00:00',
                operating_hours_end: '22:00:00',
                is_active: true,
                created_at: '2019-02-15 10:00:00'
            },
            {
                store_id: 102,
                store_code: 'BJ001',
                store_name: '北京朝阳区三里屯太古里店',
                store_type: '十人以上店铺',
                city: '北京',
                district: '朝阳区',
                address: '北京市朝阳区三里屯路19号院太古里南区2层S2-21',
                store_area: 85.00,
                rent_cost: 18000.00,
                open_date: '2020-06-15',
                in_store_zoning: 'A,B,C',
                operating_hours_start: '10:00:00',
                operating_hours_end: '22:00:00',
                is_active: true,
                created_at: '2020-05-20 10:00:00'
            },
            {
                store_id: 103,
                store_code: 'GZ001',
                store_name: '广州天河区正佳广场店',
                store_type: '大型单层店铺(30人以上)',
                city: '广州',
                district: '天河区',
                address: '广州市天河区天河路228号正佳广场4层4A001',
                store_area: 110.00,
                rent_cost: 22000.00,
                open_date: '2018-11-20',
                in_store_zoning: 'A,B,C,D',
                operating_hours_start: '10:00:00',
                operating_hours_end: '22:00:00',
                is_active: true,
                created_at: '2018-10-15 10:00:00'
            },
            {
                store_id: 104,
                store_code: 'SZ001',
                store_name: '深圳南山区海岸城店',
                store_type: '十人以上店铺',
                city: '深圳',
                district: '南山区',
                address: '深圳市南山区文心五路33号海岸城购物中心2层L2-B23',
                store_area: 75.00,
                rent_cost: 16000.00,
                open_date: '2021-04-10',
                in_store_zoning: 'A,B',
                operating_hours_start: '10:00:00',
                operating_hours_end: '22:00:00',
                is_active: true,
                created_at: '2021-03-15 10:00:00'
            },
            {
                store_id: 105,
                store_code: 'CD001',
                store_name: '成都锦江区春熙路店',
                store_type: '十人以内店铺',
                city: '成都',
                district: '锦江区',
                address: '成都市锦江区春熙路南段8号群光广场2层2F-15',
                store_area: 60.00,
                rent_cost: 12000.00,
                open_date: '2022-01-15',
                in_store_zoning: 'A,B',
                operating_hours_start: '10:00:00',
                operating_hours_end: '22:00:00',
                is_active: true,
                created_at: '2021-12-20 10:00:00'
            }
        ];

        this.stores = storeData;
        return storeData;
    }

    // 生成商品数据
    generateProducts() {
        const productCategories = {
            shoes: {
                ratio: 0.547,
                types: ['跑鞋', '篮球鞋', '休闲鞋', '训练鞋', '板鞋'],
                priceRange: [299, 1299],
                costRatio: 0.5
            },
            clothing: {
                ratio: 0.43,
                types: ['T恤', '运动裤', '外套', '运动内衣', '运动短裤'],
                priceRange: [99, 699],
                costRatio: 0.45
            },
            accessories: {
                ratio: 0.08,
                types: ['羽毛球拍', '运动包', '护具', '运动袜', '帽子'],
                priceRange: [29, 899],
                costRatio: 0.4
            }
        };

        const products = [];
        let productId = 1;

        Object.entries(productCategories).forEach(([category, config]) => {
            const count = Math.floor(100 * config.ratio); // 总共100个商品

            for (let i = 0; i < count; i++) {
                const type = this.randomChoice(config.types);
                const price = this.randomFloat(config.priceRange[0], config.priceRange[1]);
                const cost = this.randomFloat(price * config.costRatio * 0.8, price * config.costRatio * 1.2);

                const product = {
                    product_id: `P${String(productId).padStart(3, '0')}`,
                    product_name: `李宁${type}${this.randomChoice(['经典款', '新款', '限量版', '专业版', '舒适版'])}`,
                    brand: '李宁',
                    type: type,
                    category: category === 'shoes' ? '鞋类' : category === 'clothing' ? '服装' : '器材配件',
                    price: price,
                    cost: cost,
                    launch_date: this.formatDate(this.randomDate(new Date('2021-01-01'), new Date('2024-12-31'))),
                    season: this.randomChoice(['SPRING', 'SUMMER', 'AUTUMN', 'WINTER']),
                    is_active: Math.random() > 0.1, // 90%的商品在售
                    created_at: this.formatDateTime(this.randomDate(new Date('2021-01-01'), new Date('2024-12-31')))
                };

                products.push(product);
                productId++;
            }
        });

        this.products = products;
        return products;
    }

    // 生成员工数据
    generateEmployees() {
        const employees = [];
        const positions = ['store_manager', 'assistant_manager', 'sales_associate', 'cashier', 'warehouse_keeper'];
        const levels = ['junior', 'intermediate', 'senior'];
        const names = ['王婷', '李明', '张华', '刘芳', '陈强', '赵丽', '孙伟', '周敏', '吴刚', '郑红', '朱亮', '何静'];

        this.stores.forEach(store => {
            let employeeCount;
            if (store.store_type.includes('十人以内')) {
                employeeCount = this.randomInt(6, 10);
            } else if (store.store_type.includes('十人以上')) {
                employeeCount = this.randomInt(12, 20);
            } else {
                employeeCount = this.randomInt(25, 35);
            }

            for (let i = 0; i < employeeCount; i++) {
                const position = i === 0 ? 'store_manager' :
                               i === 1 && employeeCount > 10 ? 'assistant_manager' :
                               this.randomChoice(['sales_associate', 'cashier', 'warehouse_keeper']);

                const baseSalary = position === 'store_manager' ? this.randomInt(8000, 12000) :
                                 position === 'assistant_manager' ? this.randomInt(6000, 8000) :
                                 this.randomInt(3500, 5500);

                const employee = {
                    employee_id: this.counters.employeeId++,
                    employee_code: `EMP${String(this.counters.employeeId - 2000).padStart(3, '0')}`,
                    name: this.randomChoice(names),
                    gender: this.randomChoice(['M', 'F']),
                    age: this.randomInt(20, 45),
                    phone: `138${String(this.randomInt(10000000, 99999999))}`,
                    email: `emp${this.counters.employeeId - 2000}@lining.com`,
                    hire_date: this.formatDate(this.randomDate(new Date('2020-01-01'), new Date('2024-06-30'))),
                    position: position,
                    level: this.randomChoice(levels),
                    base_salary: baseSalary,
                    commission_rate: this.randomFloat(0.01, 0.03, 4),
                    store_id: store.store_id,
                    status: 'ACTIVE',
                    created_at: this.formatDateTime(this.randomDate(new Date('2020-01-01'), new Date('2024-06-30')))
                };

                employees.push(employee);
            }
        });

        this.employees = employees;
        return employees;
    }

    // 生成会员数据
    generateMembers() {
        const members = [];
        const memberNames = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十', '郑一', '王二', '冯三', '陈四'];
        const levels = ['BRONZE', 'SILVER', 'GOLD', 'PLATINUM'];

        // 每个门店生成200-500个会员
        this.stores.forEach(store => {
            const memberCount = this.randomInt(200, 500);

            for (let i = 0; i < memberCount; i++) {
                const registrationDate = this.randomDate(new Date('2020-01-01'), new Date('2024-10-31'));
                const totalConsumption = this.randomFloat(100, 50000);
                const level = totalConsumption > 20000 ? 'PLATINUM' :
                             totalConsumption > 10000 ? 'GOLD' :
                             totalConsumption > 5000 ? 'SILVER' : 'BRONZE';

                const member = {
                    member_id: `M${String(store.store_id)}${String(i + 1).padStart(4, '0')}`,
                    member_code: `VIP${String(store.store_id)}${String(i + 1).padStart(3, '0')}`,
                    name: this.randomChoice(memberNames),
                    phone: `139${String(this.randomInt(10000000, 99999999))}`,
                    gender: this.randomChoice(['M', 'F']),
                    age: this.randomInt(18, 65),
                    register_date: this.formatDate(registrationDate),
                    level: level,
                    total_consumption: totalConsumption,
                    points: Math.floor(totalConsumption * 0.1),
                    preferred_store_id: store.store_id,
                    status: Math.random() > 0.05 ? 'ACTIVE' : 'INACTIVE', // 95%活跃
                    created_at: this.formatDateTime(registrationDate)
                };

                members.push(member);
            }
        });

        this.members = members;
        return members;
    }

    // 生成促销活动数据
    generatePromotions() {
        const promotions = [];
        const promotionEvents = [
            { name: '春节大促', period: [1, 2], discount: 0.8, boost: 1.5 },
            { name: '三八女神节', period: [3, 3], discount: 0.85, boost: 1.3 },
            { name: '五一劳动节', period: [5, 5], discount: 0.9, boost: 1.2 },
            { name: '618购物节', period: [6, 6], discount: 0.75, boost: 1.6 },
            { name: '暑期促销', period: [7, 8], discount: 0.85, boost: 1.3 },
            { name: '开学季', period: [9, 9], discount: 0.9, boost: 1.2 },
            { name: '国庆黄金周', period: [10, 10], discount: 0.8, boost: 1.4 },
            { name: '双十一', period: [11, 11], discount: 0.7, boost: 1.8 },
            { name: '双十二', period: [12, 12], discount: 0.75, boost: 1.5 },
            { name: '年终清仓', period: [12, 12], discount: 0.6, boost: 1.3 }
        ];

        for (let year = 2022; year <= 2024; year++) {
            promotionEvents.forEach((event, index) => {
                const startMonth = event.period[0];
                const endMonth = event.period[1];

                // 为每个门店或全店生成促销活动
                const isGlobalPromotion = Math.random() > 0.3; // 70%是全店促销
                const targetStores = isGlobalPromotion ? [null] : this.randomChoice([this.stores]).store_id;

                const startDate = new Date(year, startMonth - 1, this.randomInt(1, 15));
                const endDate = new Date(year, endMonth - 1, this.randomInt(16, 28));

                const promotion = {
                    promotion_id: this.counters.promotionId++,
                    promotion_code: `${event.name.replace(/[^a-zA-Z0-9]/g, '_').toUpperCase()}_${year}`,
                    promotion_name: `${year}年${event.name}`,
                    store_id: isGlobalPromotion ? null : targetStores,
                    promotion_type: 'DISCOUNT',
                    discount_rate: event.discount,
                    min_amount: this.randomFloat(200, 500),
                    max_discount: this.randomFloat(100, 500),
                    start_date: this.formatDate(startDate),
                    end_date: this.formatDate(endDate),
                    expected_traffic_boost: event.boost,
                    expected_sales_boost: event.boost * 1.1,
                    status: year < 2024 ? 'EXPIRED' : (year === 2024 && endDate < new Date() ? 'EXPIRED' : 'ACTIVE'),
                    created_at: this.formatDateTime(new Date(startDate.getTime() - 7 * 24 * 60 * 60 * 1000))
                };

                promotions.push(promotion);
            });
        }

        this.promotions = promotions;
        return promotions;
    }

    // 生成排班数据
    generateSchedules() {
        const schedules = [];
        const shiftTypes = ['morning', 'afternoon', 'evening', 'full_day'];
        const positions = ['store_manager', 'assistant_manager', 'sales_associate', 'cashier', 'warehouse_keeper'];

        // 为每个员工生成2022-2024年的排班数据
        this.employees.forEach(employee => {
            const startDate = new Date('2022-01-01');
            const endDate = new Date('2024-12-31');

            for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
                // 员工不是每天都上班，根据岗位安排工作日
                const isWorkDay = employee.position === 'store_manager' ? Math.random() > 0.1 : // 店长90%上班
                                 employee.position === 'assistant_manager' ? Math.random() > 0.2 : // 副店长80%上班
                                 Math.random() > 0.3; // 其他员工70%上班

                if (!isWorkDay) continue;

                const shiftType = employee.position === 'store_manager' ? 'full_day' :
                                 this.randomChoice(shiftTypes);

                let startTime, endTime, hourlyRate;
                switch (shiftType) {
                    case 'morning':
                        startTime = '09:00:00';
                        endTime = '14:00:00';
                        hourlyRate = this.randomFloat(20, 30);
                        break;
                    case 'afternoon':
                        startTime = '14:00:00';
                        endTime = '19:00:00';
                        hourlyRate = this.randomFloat(22, 32);
                        break;
                    case 'evening':
                        startTime = '19:00:00';
                        endTime = '22:00:00';
                        hourlyRate = this.randomFloat(25, 35);
                        break;
                    case 'full_day':
                        startTime = '09:00:00';
                        endTime = '18:00:00';
                        hourlyRate = this.randomFloat(25, 40);
                        break;
                }

                const schedule = {
                    schedule_id: this.counters.scheduleId++,
                    store_id: employee.store_id,
                    employee_id: employee.employee_id,
                    schedule_date: this.formatDate(new Date(date)),
                    shift_type: shiftType,
                    start_time: startTime,
                    end_time: endTime,
                    position_code: employee.position,
                    hourly_rate: hourlyRate,
                    break_duration: shiftType === 'full_day' ? 60 : 30,
                    is_overtime: Math.random() > 0.9, // 10%加班
                    created_at: this.formatDateTime(new Date(date.getTime() - 24 * 60 * 60 * 1000))
                };

                schedules.push(schedule);
            }
        });

        this.schedules = schedules;
        return schedules;
    }

    // 生成考勤记录
    generateAttendances() {
        const attendances = [];
        const statuses = ['NORMAL', 'LATE', 'EARLY_LEAVE', 'ABSENT', 'OVERTIME'];

        this.schedules.forEach(schedule => {
            // 95%的排班会有对应的考勤记录
            if (Math.random() > 0.95) return;

            const status = this.randomChoice(statuses);
            let actualStartTime = schedule.start_time;
            let actualEndTime = schedule.end_time;
            let lateMinutes = 0;
            let earlyLeaveMinutes = 0;
            let workHours = 8;
            let overtimeHours = 0;

            // 根据状态调整实际时间
            switch (status) {
                case 'LATE':
                    lateMinutes = this.randomInt(5, 30);
                    const startTime = new Date(`2000-01-01 ${schedule.start_time}`);
                    startTime.setMinutes(startTime.getMinutes() + lateMinutes);
                    actualStartTime = this.formatTime(startTime);
                    break;
                case 'EARLY_LEAVE':
                    earlyLeaveMinutes = this.randomInt(10, 60);
                    const endTime = new Date(`2000-01-01 ${schedule.end_time}`);
                    endTime.setMinutes(endTime.getMinutes() - earlyLeaveMinutes);
                    actualEndTime = this.formatTime(endTime);
                    break;
                case 'OVERTIME':
                    overtimeHours = this.randomFloat(0.5, 3);
                    const overtimeEndTime = new Date(`2000-01-01 ${schedule.end_time}`);
                    overtimeEndTime.setHours(overtimeEndTime.getHours() + Math.floor(overtimeHours));
                    overtimeEndTime.setMinutes(overtimeEndTime.getMinutes() + (overtimeHours % 1) * 60);
                    actualEndTime = this.formatTime(overtimeEndTime);
                    break;
            }

            // 计算实际工作时长
            if (status !== 'ABSENT') {
                const start = new Date(`2000-01-01 ${actualStartTime}`);
                const end = new Date(`2000-01-01 ${actualEndTime}`);
                workHours = (end - start) / (1000 * 60 * 60) - (schedule.break_duration / 60);
            } else {
                workHours = 0;
                actualStartTime = null;
                actualEndTime = null;
            }

            const attendance = {
                attendance_id: this.counters.attendanceId++,
                employee_id: schedule.employee_id,
                store_id: schedule.store_id,
                schedule_id: schedule.schedule_id,
                attendance_date: schedule.schedule_date,
                scheduled_start_time: schedule.start_time,
                scheduled_end_time: schedule.end_time,
                actual_start_time: actualStartTime,
                actual_end_time: actualEndTime,
                status: status,
                work_hours: Math.max(0, workHours),
                overtime_hours: overtimeHours,
                late_minutes: lateMinutes,
                early_leave_minutes: earlyLeaveMinutes,
                created_at: this.formatDateTime(new Date(`${schedule.schedule_date} ${schedule.end_time}`))
            };

            attendances.push(attendance);
        });

        this.attendances = attendances;
        return attendances;
    }

    // 生成客流数据
    generateTraffic() {
        const traffic = [];
        const weatherConditions = ['SUNNY', 'CLOUDY', 'RAINY', 'SNOWY'];

        this.stores.forEach(store => {
            for (let year = 2022; year <= 2024; year++) {
                for (let month = 1; month <= 12; month++) {
                    const daysInMonth = new Date(year, month, 0).getDate();

                    for (let day = 1; day <= daysInMonth; day++) {
                        const date = new Date(year, month - 1, day);
                        const isWeekend = date.getDay() === 0 || date.getDay() === 6;
                        const isHoliday = this.isHoliday(date);

                        // 每小时的客流数据
                        for (let hour = 10; hour <= 21; hour++) { // 营业时间10:00-22:00
                            const baseTraffic = this.getBaseTraffic(store, hour, isWeekend, isHoliday);
                            const seasonalFactor = this.getSeasonalFactor(month);
                            const promotionFactor = this.getPromotionFactor(date, store.store_id);

                            const trafficIn = Math.floor(baseTraffic * seasonalFactor * promotionFactor * this.randomFloat(0.8, 1.2));
                            const trafficOut = Math.floor(trafficIn * this.randomFloat(0.85, 0.95));
                            const stayDuration = this.randomFloat(20, 80);
                            const conversionRate = this.randomFloat(0.08, 0.18);

                            const trafficRecord = {
                                traffic_id: this.counters.trafficId++,
                                store_id: store.store_id,
                                traffic_date: this.formatDate(date),
                                traffic_hour: hour,
                                traffic_count_in: trafficIn,
                                traffic_count_out: trafficOut,
                                stay_duration_avg: stayDuration,
                                conversion_rate: conversionRate,
                                weather_condition: this.randomChoice(weatherConditions),
                                temperature: this.getTemperature(month),
                                is_holiday: isHoliday,
                                is_weekend: isWeekend,
                                created_at: this.formatDateTime(new Date(date.getTime() + (hour + 1) * 60 * 60 * 1000))
                            };

                            traffic.push(trafficRecord);
                        }
                    }
                }
            }
        });

        this.traffic = traffic;
        return traffic;
    }

    // 辅助方法：获取基础客流量
    getBaseTraffic(store, hour, isWeekend, isHoliday) {
        let base = 50; // 基础客流

        // 门店规模影响
        if (store.store_type.includes('大型')) base *= 1.5;
        else if (store.store_type.includes('十人以上')) base *= 1.2;

        // 时段影响
        if (hour >= 11 && hour <= 13) base *= 1.3; // 午餐时间
        else if (hour >= 18 && hour <= 20) base *= 1.5; // 晚餐时间
        else if (hour >= 14 && hour <= 17) base *= 1.2; // 下午

        // 周末和节假日影响
        if (isWeekend) base *= 1.4;
        if (isHoliday) base *= 1.6;

        return base;
    }

    // 辅助方法：获取季节性因子
    getSeasonalFactor(month) {
        const seasonalFactors = {
            1: 1.2, 2: 1.3, 3: 1.1, 4: 1.0, 5: 1.1, 6: 1.2,
            7: 1.3, 8: 1.2, 9: 1.1, 10: 1.2, 11: 1.4, 12: 1.3
        };
        return seasonalFactors[month] || 1.0;
    }

    // 辅助方法：获取促销影响因子
    getPromotionFactor(date, storeId) {
        const activePromotions = this.promotions.filter(promo => {
            const startDate = new Date(promo.start_date);
            const endDate = new Date(promo.end_date);
            return date >= startDate && date <= endDate &&
                   (promo.store_id === null || promo.store_id === storeId);
        });

        if (activePromotions.length > 0) {
            return Math.max(...activePromotions.map(p => p.expected_traffic_boost));
        }
        return 1.0;
    }

    // 辅助方法：判断是否为节假日
    isHoliday(date) {
        const holidays = [
            '01-01', '02-11', '02-12', '02-13', '02-14', '02-15', '02-16', '02-17', // 春节
            '04-05', '05-01', '05-02', '05-03', // 清明、劳动节
            '06-22', '06-23', '06-24', // 端午节
            '09-29', '09-30', '10-01', '10-02', '10-03', '10-04', '10-05', '10-06', '10-07' // 国庆节
        ];

        const monthDay = `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
        return holidays.includes(monthDay);
    }

    // 辅助方法：获取温度
    getTemperature(month) {
        const temperatures = {
            1: [-2, 8], 2: [2, 12], 3: [8, 18], 4: [15, 25], 5: [20, 30], 6: [25, 35],
            7: [28, 38], 8: [27, 37], 9: [22, 32], 10: [15, 25], 11: [8, 18], 12: [0, 10]
        };
        const range = temperatures[month] || [15, 25];
        return this.randomFloat(range[0], range[1], 1);
    }

    // 生成交易数据
    generateTransactions() {
        const transactions = [];
        const paymentMethods = ['WECHAT_PAY', 'ALIPAY', 'CASH', 'CARD', 'UNION_PAY'];

        this.traffic.forEach(trafficRecord => {
            const transactionCount = Math.floor(trafficRecord.traffic_count_in * trafficRecord.conversion_rate);

            for (let i = 0; i < transactionCount; i++) {
                const product = this.randomChoice(this.products.filter(p => p.is_active));
                const quantity = this.randomInt(1, 3);
                const unitPrice = product.price;
                const totalAmount = unitPrice * quantity;

                // 计算折扣
                const promotionFactor = this.getPromotionFactor(new Date(trafficRecord.traffic_date), trafficRecord.store_id);
                const discountAmount = promotionFactor < 1 ? totalAmount * (1 - promotionFactor) : 0;
                const finalAmount = totalAmount - discountAmount;

                // 随机选择销售员工（该门店的员工）
                const storeEmployees = this.employees.filter(emp =>
                    emp.store_id === trafficRecord.store_id &&
                    emp.position === 'sales_associate'
                );
                const salesperson = storeEmployees.length > 0 ? this.randomChoice(storeEmployees) : null;

                // 随机选择会员（40%的交易有会员）
                const member = Math.random() < 0.4 ?
                    this.randomChoice(this.members.filter(m => m.preferred_store_id === trafficRecord.store_id)) :
                    null;

                // 生成交易时间（在该小时内随机）
                const transactionTime = new Date(trafficRecord.traffic_date);
                transactionTime.setHours(trafficRecord.traffic_hour, this.randomInt(0, 59), this.randomInt(0, 59));

                const transaction = {
                    transaction_id: this.counters.transactionId++,
                    product_id: product.product_id,
                    store_id: trafficRecord.store_id,
                    salesperson_id: salesperson ? salesperson.employee_id : null,
                    member_id: member ? member.member_id : null,
                    transaction_datetime: this.formatDateTime(transactionTime),
                    quantity: quantity,
                    unit_price: unitPrice,
                    total_amount: finalAmount,
                    discount_amount: discountAmount,
                    payment_method: this.randomChoice(paymentMethods),
                    created_at: this.formatDateTime(new Date(transactionTime.getTime() + this.randomInt(1, 300) * 1000))
                };

                transactions.push(transaction);
            }
        });

        this.transactions = transactions;
        return transactions;
    }

    // 生成预测历史记录
    generateForecastHistory() {
        const forecastHistory = [];
        const modelVersions = ['v1.0.0', 'v1.1.0', 'v1.2.0', 'v2.0.0'];

        // 为每个门店生成预测记录（每周预测一次）
        this.stores.forEach(store => {
            for (let year = 2022; year <= 2024; year++) {
                for (let month = 1; month <= 12; month++) {
                    const daysInMonth = new Date(year, month, 0).getDate();

                    // 每周生成一次预测
                    for (let day = 7; day <= daysInMonth; day += 7) {
                        const forecastDate = new Date(year, month - 1, day);
                        const targetDate = new Date(forecastDate.getTime() + 7 * 24 * 60 * 60 * 1000); // 预测一周后

                        // 随机选择预测时段
                        const forecastHour = this.randomChoice([14, 15, 16, 17, 18, 19, 20]);

                        // 获取实际数据用于对比
                        const actualTraffic = this.traffic.find(t =>
                            t.store_id === store.store_id &&
                            t.traffic_date === this.formatDate(targetDate) &&
                            t.traffic_hour === forecastHour
                        );

                        const actualSales = this.transactions
                            .filter(t =>
                                t.store_id === store.store_id &&
                                t.transaction_datetime.startsWith(this.formatDate(targetDate)) &&
                                new Date(t.transaction_datetime).getHours() === forecastHour
                            )
                            .reduce((sum, t) => sum + t.total_amount, 0);

                        const predictedTraffic = actualTraffic ?
                            Math.floor(actualTraffic.traffic_count_in * this.randomFloat(0.85, 1.15)) :
                            this.randomInt(50, 200);

                        const predictedSales = actualSales > 0 ?
                            actualSales * this.randomFloat(0.8, 1.2) :
                            this.randomFloat(5000, 25000);

                        const recommendedStaffCount = Math.ceil(predictedTraffic / 30);
                        const recommendedPositions = {
                            cashier: Math.max(1, Math.floor(recommendedStaffCount * 0.2)),
                            sales_associate: Math.max(2, Math.floor(recommendedStaffCount * 0.6)),
                            manager: 1,
                            warehouse_keeper: Math.floor(recommendedStaffCount * 0.2)
                        };

                        const accuracyScore = actualTraffic ?
                            Math.max(0.5, 1 - Math.abs(predictedTraffic - actualTraffic.traffic_count_in) / actualTraffic.traffic_count_in) :
                            this.randomFloat(0.7, 0.95);

                        const forecast = {
                            forecast_id: this.counters.forecastId++,
                            store_id: store.store_id,
                            forecast_date: this.formatDate(targetDate),
                            forecast_hour: forecastHour,
                            model_version: this.randomChoice(modelVersions),
                            predicted_traffic: predictedTraffic,
                            predicted_sales: predictedSales,
                            recommended_staff_count: recommendedStaffCount,
                            recommended_positions: JSON.stringify(recommendedPositions),
                            forecast_confidence: this.randomFloat(0.7, 0.95, 2),
                            weather_forecast: this.randomChoice(['SUNNY', 'CLOUDY', 'RAINY']),
                            temperature_forecast: this.getTemperature(targetDate.getMonth() + 1),
                            is_holiday: this.isHoliday(targetDate),
                            is_weekend: targetDate.getDay() === 0 || targetDate.getDay() === 6,
                            has_promotion: this.getPromotionFactor(targetDate, store.store_id) > 1,
                            promotion_impact_factor: this.getPromotionFactor(targetDate, store.store_id),
                            actual_traffic: actualTraffic ? actualTraffic.traffic_count_in : null,
                            actual_sales: actualSales > 0 ? actualSales : null,
                            actual_staff_count: this.randomInt(recommendedStaffCount - 2, recommendedStaffCount + 2),
                            accuracy_score: accuracyScore,
                            forecast_created_at: this.formatDateTime(forecastDate),
                            created_at: this.formatDateTime(forecastDate)
                        };

                        forecastHistory.push(forecast);
                    }
                }
            }
        });

        this.forecastHistory = forecastHistory;
        return forecastHistory;
    }

    // CSV导出功能
    exportToCSV(data, filename) {
        if (data.length === 0) return;

        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row =>
                headers.map(header => {
                    let value = row[header];
                    if (value === null || value === undefined) value = '';
                    if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
                        value = `"${value.replace(/"/g, '""')}"`;
                    }
                    return value;
                }).join(',')
            )
        ].join('\n');

        fs.writeFileSync(filename, csvContent, 'utf8');
        console.log(`已导出 ${filename}`);
    }

    // SQL建表语句生成
    generateCreateTableSQL() {
        const sqlStatements = [];

        // Store表
        sqlStatements.push(`
CREATE TABLE store (
    store_id INT PRIMARY KEY,
    store_code VARCHAR(20) NOT NULL,
    store_name VARCHAR(255) NOT NULL,
    store_type VARCHAR(100) NOT NULL,
    city VARCHAR(50) NOT NULL,
    district VARCHAR(50),
    address VARCHAR(500) NOT NULL,
    store_area DECIMAL(10,2) NOT NULL,
    rent_cost DECIMAL(10,2),
    open_date DATE NOT NULL,
    in_store_zoning VARCHAR(500),
    operating_hours_start TIME,
    operating_hours_end TIME,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);`);

        // Product表
        sqlStatements.push(`
CREATE TABLE product (
    product_id VARCHAR(50) PRIMARY KEY,
    product_name VARCHAR(255) NOT NULL,
    brand VARCHAR(100),
    type VARCHAR(100) NOT NULL,
    category VARCHAR(100) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    cost DECIMAL(10,2),
    launch_date DATE NOT NULL,
    season VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);`);

        // Employee表
        sqlStatements.push(`
CREATE TABLE employee (
    employee_id INT PRIMARY KEY,
    employee_code VARCHAR(20),
    name VARCHAR(100) NOT NULL,
    gender ENUM('M','F'),
    age INT,
    phone VARCHAR(20),
    email VARCHAR(100),
    hire_date DATE,
    position VARCHAR(30),
    level VARCHAR(20),
    base_salary DECIMAL(8,2),
    commission_rate DECIMAL(5,4),
    store_id INT,
    status ENUM('ACTIVE','INACTIVE','RESIGNED') DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (store_id) REFERENCES store(store_id)
);`);

        // Member表
        sqlStatements.push(`
CREATE TABLE member (
    member_id VARCHAR(50) PRIMARY KEY,
    member_code VARCHAR(20),
    name VARCHAR(100),
    phone VARCHAR(20),
    gender ENUM('M','F'),
    age INT,
    register_date DATE,
    level VARCHAR(20),
    total_consumption DECIMAL(10,2),
    points INT,
    preferred_store_id INT,
    status ENUM('ACTIVE','INACTIVE') DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (preferred_store_id) REFERENCES store(store_id)
);`);

        // Promotion表
        sqlStatements.push(`
CREATE TABLE promotion (
    promotion_id BIGINT PRIMARY KEY,
    promotion_code VARCHAR(30) UNIQUE,
    promotion_name VARCHAR(100) NOT NULL,
    store_id INT,
    promotion_type VARCHAR(20) NOT NULL,
    discount_rate DECIMAL(5,2),
    min_amount DECIMAL(10,2),
    max_discount DECIMAL(10,2),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    expected_traffic_boost DECIMAL(5,2),
    expected_sales_boost DECIMAL(5,2),
    status ENUM('PENDING','ACTIVE','EXPIRED','CANCELLED') DEFAULT 'PENDING',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (store_id) REFERENCES store(store_id)
);`);

        // Schedule表
        sqlStatements.push(`
CREATE TABLE schedule (
    schedule_id BIGINT PRIMARY KEY,
    store_id INT NOT NULL,
    employee_id INT NOT NULL,
    schedule_date DATE NOT NULL,
    shift_type VARCHAR(20),
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    position_code VARCHAR(30) NOT NULL,
    hourly_rate DECIMAL(8,2),
    break_duration INT,
    is_overtime BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (store_id) REFERENCES store(store_id),
    FOREIGN KEY (employee_id) REFERENCES employee(employee_id)
);`);

        // Attendance表
        sqlStatements.push(`
CREATE TABLE attendance (
    attendance_id BIGINT PRIMARY KEY,
    employee_id INT NOT NULL,
    store_id INT NOT NULL,
    schedule_id BIGINT,
    attendance_date DATE NOT NULL,
    scheduled_start_time TIME,
    scheduled_end_time TIME,
    actual_start_time TIME,
    actual_end_time TIME,
    status VARCHAR(20),
    work_hours DECIMAL(4,1),
    overtime_hours DECIMAL(4,1),
    late_minutes INT,
    early_leave_minutes INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (employee_id) REFERENCES employee(employee_id),
    FOREIGN KEY (store_id) REFERENCES store(store_id),
    FOREIGN KEY (schedule_id) REFERENCES schedule(schedule_id)
);`);

        // Traffic表
        sqlStatements.push(`
CREATE TABLE traffic (
    traffic_id BIGINT PRIMARY KEY,
    store_id INT NOT NULL,
    traffic_date DATE NOT NULL,
    traffic_hour TINYINT NOT NULL,
    traffic_count_in INT,
    traffic_count_out INT,
    stay_duration_avg DECIMAL(5,2),
    conversion_rate DECIMAL(5,4),
    weather_condition VARCHAR(50),
    temperature DECIMAL(4,1),
    is_holiday BOOLEAN,
    is_weekend BOOLEAN,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (store_id) REFERENCES store(store_id)
);`);

        // Transaction表
        sqlStatements.push(`
CREATE TABLE transaction (
    transaction_id BIGINT PRIMARY KEY,
    product_id VARCHAR(50),
    store_id INT,
    salesperson_id INT,
    member_id VARCHAR(50),
    transaction_datetime DATETIME,
    quantity INT,
    unit_price DECIMAL(10,2),
    total_amount DECIMAL(10,2),
    discount_amount DECIMAL(10,2),
    payment_method VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES product(product_id),
    FOREIGN KEY (store_id) REFERENCES store(store_id),
    FOREIGN KEY (salesperson_id) REFERENCES employee(employee_id),
    FOREIGN KEY (member_id) REFERENCES member(member_id)
);`);

        // Forecast_History表
        sqlStatements.push(`
CREATE TABLE forecast_history (
    forecast_id BIGINT PRIMARY KEY,
    store_id INT,
    forecast_date DATE,
    forecast_hour TINYINT,
    model_version VARCHAR(20),
    predicted_traffic INT,
    predicted_sales DECIMAL(10,2),
    recommended_staff_count INT,
    recommended_positions JSON,
    forecast_confidence DECIMAL(5,2),
    weather_forecast VARCHAR(50),
    temperature_forecast DECIMAL(4,1),
    is_holiday BOOLEAN,
    is_weekend BOOLEAN,
    has_promotion BOOLEAN,
    promotion_impact_factor DECIMAL(5,2),
    actual_traffic INT,
    actual_sales DECIMAL(10,2),
    actual_staff_count INT,
    accuracy_score DECIMAL(5,2),
    forecast_created_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (store_id) REFERENCES store(store_id)
);`);

        return sqlStatements;
    }
}

// 创建生成器实例
const generator = new MockDataGenerator();

// 生成基础数据
console.log('开始生成Mock数据...');
console.log('生成门店数据...');
const stores = generator.generateStores();
console.log(`生成了 ${stores.length} 个门店`);

console.log('生成商品数据...');
const products = generator.generateProducts();
console.log(`生成了 ${products.length} 个商品`);

console.log('生成员工数据...');
const employees = generator.generateEmployees();
console.log(`生成了 ${employees.length} 个员工`);

console.log('生成会员数据...');
const members = generator.generateMembers();
console.log(`生成了 ${members.length} 个会员`);

console.log('生成促销活动数据...');
const promotions = generator.generatePromotions();
console.log(`生成了 ${promotions.length} 个促销活动`);

console.log('生成排班数据...');
const schedules = generator.generateSchedules();
console.log(`生成了 ${schedules.length} 条排班记录`);

console.log('生成考勤数据...');
const attendances = generator.generateAttendances();
console.log(`生成了 ${attendances.length} 条考勤记录`);

console.log('生成客流数据...');
const traffic = generator.generateTraffic();
console.log(`生成了 ${traffic.length} 条客流记录`);

console.log('生成交易数据...');
const transactions = generator.generateTransactions();
console.log(`生成了 ${transactions.length} 条交易记录`);

console.log('生成预测历史数据...');
const forecastHistory = generator.generateForecastHistory();
console.log(`生成了 ${forecastHistory.length} 条预测记录`);

// 创建输出目录
const outputDir = './output';
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir);
}

// 导出CSV文件
console.log('\n开始导出CSV文件...');
generator.exportToCSV(stores, path.join(outputDir, 'store.csv'));
generator.exportToCSV(products, path.join(outputDir, 'product.csv'));
generator.exportToCSV(employees, path.join(outputDir, 'employee.csv'));
generator.exportToCSV(members, path.join(outputDir, 'member.csv'));
generator.exportToCSV(promotions, path.join(outputDir, 'promotion.csv'));
generator.exportToCSV(schedules, path.join(outputDir, 'schedule.csv'));
generator.exportToCSV(attendances, path.join(outputDir, 'attendance.csv'));
generator.exportToCSV(traffic, path.join(outputDir, 'traffic.csv'));
generator.exportToCSV(transactions, path.join(outputDir, 'transaction.csv'));
generator.exportToCSV(forecastHistory, path.join(outputDir, 'forecast_history.csv'));

// 生成SQL建表语句
console.log('\n生成SQL建表语句...');
const sqlStatements = generator.generateCreateTableSQL();
const sqlContent = sqlStatements.join('\n\n');
fs.writeFileSync(path.join(outputDir, 'create_tables.sql'), sqlContent, 'utf8');
console.log('已生成 create_tables.sql');

console.log('\n数据生成完成！');
console.log('输出文件位置：./output/');
console.log('包含以下文件：');
console.log('- 10个CSV数据文件');
console.log('- 1个SQL建表文件 (create_tables.sql)');

// 统计信息
console.log('\n数据统计：');
console.log(`门店数量: ${stores.length}`);
console.log(`商品数量: ${products.length}`);
console.log(`员工数量: ${employees.length}`);
console.log(`会员数量: ${members.length}`);
console.log(`促销活动: ${promotions.length}`);
console.log(`排班记录: ${schedules.length}`);
console.log(`考勤记录: ${attendances.length}`);
console.log(`客流记录: ${traffic.length}`);
console.log(`交易记录: ${transactions.length}`);
console.log(`预测记录: ${forecastHistory.length}`);

// 计算总销售额
const totalSales = transactions.reduce((sum, t) => sum + t.total_amount, 0);
console.log(`\n业务指标：`);
console.log(`总销售额: ¥${(totalSales / 100000000).toFixed(2)}亿元`);
console.log(`平均客单价: ¥${(totalSales / transactions.length).toFixed(2)}`);
console.log(`总客流量: ${traffic.reduce((sum, t) => sum + t.traffic_count_in, 0)}人次`);
console.log(`平均转化率: ${(traffic.reduce((sum, t) => sum + t.conversion_rate, 0) / traffic.length * 100).toFixed(2)}%`);
